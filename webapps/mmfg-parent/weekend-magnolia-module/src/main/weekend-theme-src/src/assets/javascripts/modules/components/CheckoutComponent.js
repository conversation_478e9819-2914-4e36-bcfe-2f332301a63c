import Component from '../abstracts/Component';
import CreditCardValidator from './CreditCardValidator';
//import LoaderUtil from '../utils/LoaderUtil';
import TriggeredLabelsComponent from "./TriggeredLabelsComponent";

export default class CheckoutComponent extends Component {

  get SELECTORS() {
    return {
      // general
      checkoutForm: '#checkoutForm',
      // SHIPPING ADRESSES
      shippingAddressWrapper: '.checkout-shipping-address ',
      shippingAddressSelector: 'input[name=shippingAddressCode]',
      shippingAddressNewSelector: '.js-new-shipping-address',
      shippingAddressBoxSelector: '#add-new-shipping-address',
      shippingRegion: '#add-new-shipping-address .country input',
      // INVOICE
      invoiceCheckboxWrapper: '#invoice-checkbox-wrapper',
      needInvoiceSelector: '.js-need-invoice',
      needInvoiceFastSelector : '.js-fast-need-invoice', // fast checkout
      // BILLING
      billingAddressSelector: 'input[name=billingAddressCode]',
      billingAddressCompleteBoxSelector: '#billing-addresses',
      billingAddressComplete: '#complete-address',
      billingAddressNewSelector: '.js-new-billing-address',
      billingAddressNewBox: '#new-billing-address',
      checkoutModifyAddress:".checkout-modify-address-box",
      // logged when you have 1 or more billing address
      billingAddresses: '#billing-addresses .checkout-billing-address',
      billingAddressAddNew: '#new-billing-addresses-box',
      billingAddressAddNewSelector: '.js-add-billing-address',
      // NEW BILLING
      billingRegion: '#new-billing-address .country input', // ,#billingAddress.country , #billingAddress.country.isocode
      billingRegionSelect: '#new-billing-address .country select', // ,#billingAddress.country , #billingAddress.country.isocode
      billingRegionNew: '#new-billing-address-box .countries input', // ,#billingAddress.country , #billingAddress.country.isocode

      // PAYMENT METHODS
      paymentMethodSelector: '.payment-method-container .payment-method',
      paymentMethodInput: 'input[name=paymentMethodCode]',
      paymentMethodContainer: '.payment-method-container',

      // PRIVACY
      privacyInputs: '[name="terms"], [name="terms2"]',

      // BUTTONS
      anchorSubmitSelector: '.js-button-submit',
      buttonSubmitSelector: '#js-button-submit',

      aHrefSubmitSelector: 'a.cta-primary',
      spanTextSubmitSelector: 'span[data-cta-text]',

      formSubmitSelector: '.form-submit',

      // axerve buttons
      anchorSubmitSelectorAxerve : '.js-button-submit-axerve',
      buttonSubmitSeletorAxerve : '#js-button-submit-axerve',

      // CART SUMMARY
      cartSummaryPriceDiv: '#totalWithTax',
      cartSummaryTotalPrice: '.total-price .c-p-card__details__prices .price',
      // COD
      cartSummaryCod: '.js-cashOnDeliveryChecked',

      // BRAINTREE
      braintreeButtonSelector: 'input.js-braintree-payment',
      braintreeOverlay: ".loader",
      braintreePaypalBtn: "#paypal-button-container",
      shippingAddressGiftCardValue: "input[name='shippingAddressIsNotEmpty']",

      // PAYPAL
      paypalButtonSelector: '.js-paypal-payment',
      paypalErrorBoxSelector: '.js-paypal-error-box',
      paypalErrorMessageSelector: '.js-paypal-message-box',

      // GC SECTION
      giftcardIntegrationPaymentSelector: '.js-gc-payment' ,
      giftcardForm: '.js-giftcard-form' ,
      giftcardAddmorePaypalForm: '.giftcard-addmore-paypal-form' ,
      paymentGiftcardForm:'[data-form-id="paymentGiftcardForm"]',
      paymentGiftcardFormById: '#paymentGiftcardForm',

      // CC
      paymentTokenFlag: 'input[name=payment_token_flag]',
      newCardPanel: '.checkout-billing-newcard',
      newCardSave: '.js-save-card',
      paymentCards: '.payment-card',
      paymentcreditCardForm: '#paymentCreditCardForm',

      // C&C
      addressClickAndCollect: '.cac-address',

      // FAST
      fastAddress: '.new-address-fast',

    };
  }

  get CLASSES() {
    return {
      active: 'active',
      opened: 'opened',
      unavailable: 'not-available',
      hiddenClass: 'hidden',
      fastClass: 'fast',
      addingClass: 'adding'
    };
  }

  static setProvinceField (countryCode) {
    // variabile aggiunta per gestire la province US che sparisce BIA-1776
    if(countryCode == null){
      countryCode = document.getElementsByTagName('body')[0].getAttribute("data-website");
    }
    switch(countryCode) {
      case "IT": {
        let $province = $(".provinceIt");
        $(".stateUs").prop('disabled', true).parents(".form-group").hide().removeClass("used");
        $(".province").prop('disabled', true).parents(".form-group").hide().removeClass("used");
        $province.parent().find("label").html($province.data("label"));
        $province.prop('disabled', false).parents(".form-group").show().addClass("used");
        break;
      }
      case "US": {
        let $province = $(".stateUs");
        $(".provinceIt").prop('disabled', true).parents(".form-group").hide().removeClass("used");
        $(".province").prop('disabled', true).parents(".form-group").hide().removeClass("used");
        $province.prop('disabled', false).parents(".form-group").show().addClass("used");
        $province.parent().find("label").html($province.data("label"));
        break;
      }
      default: {
        let $province = $(".province");
        $(".stateUs").prop('disabled', true).parents(".form-group").hide().removeClass("used");
        $(".provinceIt").prop('disabled', true).parents(".form-group").hide().removeClass("used");
        $province.prop('disabled', false).parents(".form-group").show().addClass("used");
        $province.parent().find("label").html($province.data("label"));
      }
    }
  }

  setProvinceField(countryCode) {
    this.log("setProvinceField " + countryCode);
    CheckoutComponent.setProvinceField(countryCode)
  }

  // Intercetta le chiamate a Google Analytics
  interceptGoogleAnalytics() {
    // Intercetta fetch
    const originalFetch = window.fetch;
    window.fetch = function(...args) {
      const url = args[0];
      if (typeof url === 'string' && url.includes('google-analytics.com/g/collect')) {
        console.log('🔍 Google Analytics fetch call intercepted:', {
          url: url,
          method: args[1]?.method || 'GET',
          body: args[1]?.body,
          headers: args[1]?.headers,
          fullRequest: args
        });
      }
      return originalFetch.apply(this, args);
    };

    // Intercetta XMLHttpRequest
    const originalXHROpen = XMLHttpRequest.prototype.open;
    const originalXHRSend = XMLHttpRequest.prototype.send;

    XMLHttpRequest.prototype.open = function(method, url, ...args) {
      this._interceptedUrl = url;
      this._interceptedMethod = method;
      return originalXHROpen.apply(this, [method, url, ...args]);
    };

    XMLHttpRequest.prototype.send = function(data) {
      if (this._interceptedUrl && this._interceptedUrl.includes('google-analytics.com/g/collect')) {
        console.log('🔍 Google Analytics XHR call intercepted:', {
          url: this._interceptedUrl,
          method: this._interceptedMethod,
          data: data,
          headers: this.getAllResponseHeaders ? this.getAllResponseHeaders() : 'N/A'
        });
      }
      return originalXHRSend.apply(this, arguments);
    };

    // Intercetta anche navigator.sendBeacon se utilizzato
    if (navigator.sendBeacon) {
      const originalSendBeacon = navigator.sendBeacon;
      navigator.sendBeacon = function(url, data) {
        if (url.includes('google-analytics.com/g/collect')) {
          console.log('🔍 Google Analytics sendBeacon call intercepted:', {
            url: url,
            data: data
          });
        }
        return originalSendBeacon.apply(this, arguments);
      };
    }
  }

  // CONSTRUCTOR
  constructor(htmlEl) {
    super(htmlEl);
    this.detachAnchorTag = null;
    this.detachButton = null;

    // Intercetta le chiamate a Google Analytics
    this.interceptGoogleAnalytics();
    // elements
    this.anchorSubmitSelector = this.$component.querySelector(this.SELECTORS.anchorSubmitSelector);
    this.anchorSubmitSelectorAxerve = this.$component.querySelector(this.SELECTORS.anchorSubmitSelectorAxerve);
    this.aHrefSubmitSelector = this.$component.querySelector(this.SELECTORS.aHrefSubmitSelector);
    this.formSubmitSelector = this.$component.querySelector(this.SELECTORS.formSubmitSelector);
    this.spanTextSubmitSelector = this.$component.querySelector(this.SELECTORS.spanTextSubmitSelector);
    this.cartSummaryTotalPrice = this.$component.querySelector(this.SELECTORS.cartSummaryTotalPrice);
    this.cartSummaryPriceDiv = this.$component.querySelector(this.SELECTORS.cartSummaryPriceDiv);
    this.cartSummaryCod = this.$component.querySelector(this.SELECTORS.cartSummaryCod);
    this.braintreePaypalBtn = this.$component.querySelector(this.SELECTORS.braintreePaypalBtn);
    this.invoiceCheckboxWrapper = this.$component.querySelector(this.SELECTORS.invoiceCheckboxWrapper);
    this.needInvoiceSelector = this.$component.querySelector(this.SELECTORS.needInvoiceSelector);
    this.checkoutModifyAddress = this.$component.querySelector(this.SELECTORS.checkoutModifyAddress);
    this.billingAddressNewSelector = this.$component.querySelector(this.SELECTORS.billingAddressNewSelector);
    if (this.$component.querySelector(this.SELECTORS.shippingAddressGiftCardValue) != null) {
      this.shippingAddressGiftCardValue = this.$component.querySelector(this.SELECTORS.shippingAddressGiftCardValue).value;
    }
    this.paypalButtonSelector = this.$component.querySelector(this.SELECTORS.paypalButtonSelector);
    this.checkoutForm = this.$component.querySelector(this.SELECTORS.checkoutForm);
    // multiple elements
    this.privacyInputs = this.$component.querySelectorAll(this.SELECTORS.privacyInputs);
    this.paymentCards = this.$component.querySelectorAll(this.SELECTORS.paymentCards);
    this.paymentCreditCardForm = this.$component.querySelector(this.SELECTORS.paymentcreditCardForm);
    this.paymentGiftcardForm = this.$component.querySelector(this.SELECTORS.paymentGiftcardForm);
    this.paymentGiftcardFormById = this.$component.querySelector(this.SELECTORS.paymentGiftcardFormById);
  }

  // enable different billing address checkbox
  enableNewBillingAddressSelector(enable = true ,box){
    console.log(`🏠 enableNewBillingAddressSelector called with enable=${enable}`, {
      box: box,
      boxDataShow: box?.getAttribute('data-show')
    });

    // if i enable the invoice address, i enable the new billing address checkbox
    let billingAddressNewSelector = this.$component.querySelector(this.SELECTORS.billingAddressNewSelector);
    let billingAddressSelector = this.$component.querySelector(this.SELECTORS.billingAddressSelector);
    let billingAddresses = this.$component.querySelectorAll(this.SELECTORS.billingAddresses);
    let boxPrefix = box.getAttribute('data-show');
    // Selettore più ampio per catturare tutti i campi di fatturazione
    let fields = this.$component.querySelectorAll(boxPrefix + " input, " + boxPrefix + " select");

    console.log(`🏠 Selector used: "${boxPrefix} input, ${boxPrefix} select"`);
    console.log(`🏠 All found fields:`, Array.from(fields).map(f => ({ name: f.name, type: f.type, required: f.required })));

    console.log(`🏠 Found elements:`, {
      billingAddressNewSelector: !!billingAddressNewSelector,
      billingAddressSelector: !!billingAddressSelector,
      billingAddresses: billingAddresses.length,
      boxPrefix: boxPrefix,
      fields: fields.length
    });

    //
    if (billingAddressNewSelector != null || $('.province-us').length > 0 ) {
      if (enable) {
        // if i have no choiches ( billing or new billing)
        if (billingAddresses.length == 0) {
          console.log('🏠 ENABLING billing address (no existing addresses)');
          // enable the selectors
          if (this.$component.querySelector(boxPrefix + " .customer-billing-types select") != null) this.$component.querySelector(boxPrefix + " .customer-billing-types select").disabled =  false;
          if (this.$component.querySelector(boxPrefix + " .billing-countries select") != null) this.$component.querySelector(boxPrefix + " .billing-countries select").disabled =  false;
          // enable new billing address

          if (typeof billingAddressNewSelector !== 'undefined' && billingAddressNewSelector != null) billingAddressNewSelector.disabled =  false;
          // enable billing fields
          this.enableFields(fields,true);
          // set the provinces
          // if (this.$component.querySelector(this.SELECTORS.billingRegion) != null) this.setProvinceField( this.$component.querySelector(this.SELECTORS.billingRegion).value );
          // launch event
          this.$emit(this.$customEvents.CHECKOUT_EVENTS.showInvoiceFormLabels);
        } else {
          console.log('🏠 ENABLING billing address selector (existing addresses found)');
          // enable the radio for the billing
          if (typeof billingAddressNewSelector !== 'undefined' && billingAddressNewSelector != null) billingAddressSelector.disabled = false;
        }
      } else {
        console.log('🏠 DISABLING billing address');
        // disable the 2 selectors
        // if (this.$component.querySelector(boxPrefix + " .customer-billing-types select") != null) this.$component.querySelector(boxPrefix + " .customer-billing-types select").disabled = true;
        // if (this.$component.querySelector(boxPrefix + " .billing-countries select") != null) this.$component.querySelector(boxPrefix + " .billing-countries select").disabled = true;
        //
        // if (typeof billingAddressNewSelector !== 'undefined' && billingAddressNewSelector != null) billingAddressNewSelector.disabled =  true;
        // disable billing fields
        this.enableFields(fields,false);
      }
    }
  }

  // enable form fields
  enableFields(fields,enable = true) {
    console.log(`🔧 enableFields called with enable=${enable}, fields count=${fields?.length || 0}`);

    if (fields != null && fields.length > 0) {
      fields.forEach(field => {
        const fieldInfo = {
          name: field.name,
          wasRequired: field.required,
          wasDisabled: field.disabled,
          hasOriginalRequired: field.hasAttribute('data-original-required')
        };

        if (enable) {
          // this.log('enable ',field);
          field.disabled = false;
          // Ripristina l'attributo required se era presente originariamente
          if (field.hasAttribute('data-original-required')) {
            field.required = true;
            field.removeAttribute('data-original-required');
          }
          console.log(`✅ ENABLED field:`, {
            ...fieldInfo,
            nowRequired: field.required,
            nowDisabled: field.disabled
          });
        } else {
          // this.log('disable ',field);
          // Salva lo stato originale dell'attributo required prima di disabilitare
          if (field.required) {
            field.setAttribute('data-original-required', 'true');
            field.required = false;
          }
          field.disabled = true;
          console.log(`❌ DISABLED field:`, {
            ...fieldInfo,
            nowRequired: field.required,
            nowDisabled: field.disabled
          });
        }
      });
    }
  }

  restyleModalDynamically(modalContent) {
    // toggle class that changes style in case of overflow
    const observer = new ResizeObserver(function () {
      console.log("height of modal changed to:", modalContent.offsetHeight);
      if (modalContent.offsetHeight > window.innerHeight) {
        modalContent.classList.add('overflowing');
      } else {
        modalContent.classList.remove('overflowing');
      }
    });

    observer.observe(modalContent);

    this.$on(this.$customEvents.MODAL_EVENTS.afterClose, () => {
      observer.disconnect();
    });
  }

  // payment radio selection
  paymentSelection() {
    let paymentMethods = this.$component.querySelectorAll(this.SELECTORS.paymentMethodSelector);
    //
    if (paymentMethods != null && paymentMethods.length > 0) {
      paymentMethods.forEach(paymentMethod => {

        //WE3-953 checks active radio to avoid bad request
        if (paymentMethod.classList.contains(this.CLASSES.active)) {
          console.log('CHECKED=TRUE');
          paymentMethod.querySelector(this.SELECTORS.paymentMethodInput).checked = true;
        }

        paymentMethod.addEventListener("click", ()=>{
          // check if is available or is active
          if ( paymentMethod.classList.contains(this.CLASSES.unavailable) || paymentMethod.classList.contains(this.CLASSES.active) ) return false;
          // remove active class
          let activeMethod = this.$component.querySelector(this.SELECTORS.paymentMethodContainer).querySelector('.'+this.CLASSES.active);
          if (activeMethod != null) {
            activeMethod.classList.remove(this.CLASSES.active);
          }
          // adding active class
          paymentMethod.classList.add(this.CLASSES.active);
          // selecting radio
          if (paymentMethod.querySelector(this.SELECTORS.paymentMethodInput).getAttribute('disabled') != 'disabled') {
            paymentMethod.querySelector(this.SELECTORS.paymentMethodInput).checked = true;
          }
          // if not GC
          if(!paymentMethod.querySelector(this.SELECTORS.paymentMethodInput).classList.contains(this.SELECTORS.giftcardIntegrationPaymentSelector.replace('.', ''))) {
            // fire payment change event event
            this.$emit(this.$customEvents.CHECKOUT_EVENTS.paymentSelected,{payment: paymentMethod});
          } else {
            // with the GC i submit the form, to change the payment type
            this.$component.querySelector(this.SELECTORS.giftcardForm).submit();
          }
        });
        if(paymentMethod.querySelector(this.SELECTORS.paymentMethodInput).checked && !paymentMethod.hasAttribute("data-js-giftcard")) {
          this.updatePaymentMethod(paymentMethod);
        }
      });
    }
  }

  // privacy checkboxes select
  privacyCheckSelect() {
    let checkboxes = this.$component.querySelectorAll(this.SELECTORS.privacyInputs);
    if (checkboxes != null) {
      checkboxes.forEach(checkbox => {
        checkbox.addEventListener("change", ()=>{
          // event
          this.$emit(this.$customEvents.CHECKOUT_EVENTS.privacyChange,{checkboxes: checkboxes});
        });
      });
    }
  }

  // enable submit
  enableCheckoutSubmit(checkboxes) {
    // fastcheckout error check
    let isError = this.$component.classList.contains('checkout-page') && this.$component.classList.contains('fast') && this.$component.querySelector('.address-invalid') != null;
    console.log('Error on fast checkout address: ' , isError);

    // privacy loop
    if (checkboxes != null) {
      let nPrivacy = 0;
      checkboxes.forEach(checkbox => {
        if (checkbox.checked) {
          nPrivacy++;
        }
      });
      // if all privacy are checked
      if (nPrivacy == checkboxes.length && !isError) {
        // enable submit button
        if(this.anchorSubmitSelector !== null)
        {
          this.anchorSubmitSelector.disabled = false;
          this.anchorSubmitSelector.removeAttribute('disabled');
          this.anchorSubmitSelector.classList.remove('disabled');
        }
        if(this.anchorSubmitSelectorAxerve !== null)
        {
          this.anchorSubmitSelectorAxerve.disabled = false;
          this.anchorSubmitSelectorAxerve.removeAttribute('disabled');
          this.anchorSubmitSelectorAxerve.classList.remove('disabled');
        }
        // paypal button
        if (this.braintreePaypalBtn != null) {
          this.braintreePaypalBtn.setAttribute('tabindex', 0);
          this.braintreePaypalBtn.classList.remove('paypal-button-disabled');
        }
      } else {
        // disable submit button
        if(this.anchorSubmitSelector !== null)
        {
          this.anchorSubmitSelector.disabled = true;
          this.anchorSubmitSelector.setAttribute('disabled','disabled');
          this.anchorSubmitSelector.classList.add('disabled');
        }
        if(this.anchorSubmitSelectorAxerve !== null)
        {
          this.anchorSubmitSelectorAxerve.disabled = true;
          this.anchorSubmitSelectorAxerve.setAttribute('disabled','disabled');
          this.anchorSubmitSelectorAxerve.classList.add('disabled');
        }
        // paypal button
        if (this.braintreePaypalBtn != null) {
          this.braintreePaypalBtn.setAttribute('tabindex', -1);
          this.braintreePaypalBtn.classList.add('paypal-button-disabled');
        }
      }
    }
  }

  // payment total check function
  paymentTotalCheck(paymentMethod) {
    let ctx = this;
    // general divs
    let divTotal = this.cartSummaryPriceDiv;
    let divTotalprice = this.cartSummaryTotalPrice;
    // USELESSSSS!!!!!
    let getData = {};
    // specific div
    let valuepayment = paymentMethod.querySelector(this.SELECTORS.paymentMethodInput).value;
    //
    let contextPath = document.getElementsByTagName('HTML')[0].getAttribute("data-context");
    $.ajax({
      type: "GET",
      url: contextPath + "/checkout?cod-cost=true&payment=" + valuepayment,
      data: getData,
      dataType: "json",
      success: function (response) {
        ctx.log(valuepayment+` AJAX response: ${response}`);
        $('#checkoutForm #cartHash').val(response.cartHash);
        // if i have the total shown , i faded, put the value , and reshow
        if (divTotalprice != null) {
          divTotalprice.classList.add('faded');
          setTimeout(function(){
            divTotalprice.innerHTML = response.totalPrice;
            divTotalprice.classList.remove('faded');
          }, 700);
        }
      },
      error: function (xhr) {
        console.log(valuepayment+" AJAX error:");
      }
    }).then(res => {
      // launch change payment event
      ctx.changePaymentEvent(paymentMethod);
    }).catch(err => {
      console.log(err);
    });
  }

  // change payment button
  changePaymentEvent(paymentMethod){
    // payment type
    let valuepayment = paymentMethod.querySelector(this.SELECTORS.paymentMethodInput).value;
    // if is COD
    if (valuepayment == 'cashOnDelivery') {
      this.cartSummaryCod.classList.remove('hidden');
    } else {
      this.cartSummaryCod.classList.add('hidden');
    }

    // update anchor submit button
    let parentContainer = $(".form-actions .form-submit");
    if (valuepayment == 'braintree_paypal' && this.braintreePaypalBtn.classList.contains('hidden')) {
      this.log('braintreee');
      // fake button
      this.detachAnchorTag = parentContainer.find(this.SELECTORS.anchorSubmitSelector).detach();
      // real button
      this.detachButton = parentContainer.find(this.SELECTORS.buttonSubmitSelector).detach();
      this.braintreePaypalBtn.classList.remove('hidden');
    } else {
      this.log('NO braintreee');
      parentContainer.append(this.detachAnchorTag);
      // this.detachAnchorTag = null;
      parentContainer.append(this.detachButton);
      // this.detachButton = null;
      if (this.braintreePaypalBtn != null) {
        this.braintreePaypalBtn.classList.add('hidden');
      }
    }
    if (valuepayment == 'axerve_klarna') {
      this.formSubmitSelector.classList.add('axerve-klarna');
      this.aHrefSubmitSelector.classList.add('axerve-klarna');
      this.spanTextSubmitSelector.setAttribute("data-cta-text", this.aHrefSubmitSelector.getAttribute("data-axerve-klarna-label"));
      this.spanTextSubmitSelector.innerHTML = this.aHrefSubmitSelector.getAttribute("data-axerve-klarna-label");
    } else {
      this.formSubmitSelector.classList.remove('axerve-klarna');
      this.aHrefSubmitSelector.classList.remove('axerve-klarna');
      this.spanTextSubmitSelector.setAttribute("data-cta-text", this.aHrefSubmitSelector.getAttribute("data-std-label"));
      this.spanTextSubmitSelector.innerHTML = this.aHrefSubmitSelector.getAttribute("data-std-label");
    }
    // update submit form button
    this.enableCheckoutSubmit(this.privacyInputs);
  }


  // update payment method
  updatePaymentMethod(paymentMethod) {
    // ajax call to check the total for the payment
    this.paymentTotalCheck(paymentMethod);
    //  WEEKEND3
    //  paymentMethodCode1 Credit Card
    //  paymentMethodCode2 PAYPAL
    //  paymentMethodCode3 gift card
    //  paymentMethodCode4 -> cash on delivery
    //  paymentMethodCode5 -> gift card ?
    //  paymentMethodCode3 -> gift card ?
  }

  // submit form
  submitCheckoutForm() {
    let ctx = this;
    // TO DO SUBMIT
    $(document).on('click keydown', this.SELECTORS.anchorSubmitSelector, { selectors: this.SELECTORS }, function (event) {

      const key = event.key;
      //prevent key press if <> Enter
      if (key && key !== "Enter") {
        return false
      }
      // prevent default behaviour
      event.preventDefault();
      // vars
      let selectors = event.data.selectors;
      let $cta = $(this);
      // if is disabled
      if($cta.attr('disabled')){ return; }
      // if paypal is selected - i jumped a step -> go to paypal and then confirm page
      if ($cta.hasClass('js-check-paypal')) {
        // PayPal
        let href = $cta.data('paypalcheckurl');
        let isPaypal = (ctx.paypalButtonSelector != null ) ? ctx.paypalButtonSelector.checked : false;
        let fast = $cta.hasClass('js-button-submit-fast');

        //
        if (fast) {
          $(selectors.buttonSubmitSelector).trigger('click');
        }  else if (isPaypal) {
          // reset paypal messages
          $(selectors.paypalErrorMessageSelector).html('');
          $(selectors.paypalErrorMessageSelector).addClass('hidden');
          $(selectors.paypalErrorBoxSelector).removeClass('has-error');
          // disable submit
          $('.js-button-submit').attr('disabled',true);
          // check paypal availability
          $.ajax({
            url: href,
            data: $('#checkoutForm').serialize(),
            dataType: "html",
            success: function (response) {
              response = $.trim(response);
              // ctx.$emit(ctx.$customEvents.LOADER_EVENTS.hideLocal, { button: $cta} )
              console.log(`Paypal AJAX response: ${response}`);
              var responseArray = response.split('|');
              var responseResult = responseArray[0];
              if (responseResult === "KO") {
                console.log(' submit form');
                $("#checkoutForm").submit();
              } else if (responseResult === "OK_RT") {
                console.log(' submit form');
                $('#checkoutForm #cartHash').val(responseArray[1]);
                $("#checkoutForm").submit();
              } else if (responseResult === "OK_IC") {
                // trigger paypal buttom
                $(".paypal-button").click();
              } else {
                $(selectors.paypalErrorMessageSelector).html(responseResult);
                $(selectors.paypalErrorMessageSelector).removeClass('hidden');
                $(selectors.paypalErrorBoxSelector).addClass('has-error');
                $('.js-button-submit').attr('disabled',false);
              }
            },
            error: function (xhr) {
              console.log("Paypal AJAX error:", arguments);
              // LoaderUtil.hideAllLoaderContainers();
              ctx.$emit(ctx.$customEvents.LOADER_EVENTS.hideLocal, { button: $cta} );
              $('.js-button-submit').attr('disabled',false);
            }
          });
        } else {
          CreditCardValidator.CREDIT_CARD_VALIDATOR.validator($("#cardNumber"));
          //
          $(selectors.buttonSubmitSelector).trigger('click');
        }
      } else {
        // CC step with CC form
        let selectedCardRadio = $(selectors.paymentTokenFlag + ':checked');
        if (selectedCardRadio.length > 0 && selectedCardRadio.attr('value') !== "newcard") {
          console.log("saved credit card found, radio is", selectedCardRadio.attr('value'));

          let form = selectedCardRadio.closest('form');
          let cvcNumber = $('[name=card_cvn]',form);
          if( cvcNumber.val() == ''){
            cvcNumber.addClass('error');
            return;
          }
          cvcNumber.removeClass('error');
          //
          form.submit();
        } else {
          //
          CreditCardValidator.CREDIT_CARD_VALIDATOR.validator($("#cardNumber"));
          //
          $(selectors.buttonSubmitSelector).trigger('click');
        }
      }
    });
  }

  // submit axerve form
  submitAxerveCheckoutForm() {
    let ctx = this;
    // TO DO SUBMIT
    $(document).on('click', this.SELECTORS.anchorSubmitSelectorAxerve, { selectors: this.SELECTORS }, function (event) {
      // prevent default behaviour
      event.preventDefault();
      // vars
      let selectors = event.data.selectors;
      let $cta = $(this);
      // if is disabled
      if($cta.attr('disabled')){ return; }

      $(ctx.SELECTORS.anchorSubmitSelectorAxerve).attr("disabled", true);

      var radios = document.getElementsByName('payment_token_flag');
      var creditcardToken;
      for (var i = 0; i < radios.length; i++) {
        if(radios[i].checked && radios[i].value !== 'newcard')
        {
          creditcardToken = radios[i].value;
          break;
        }
      }

      var axerveBodyRequest={};
      var fast = $(this).hasClass('js-button-submit-fast');
      if (fast || creditcardToken)
      {
        axerveBodyRequest = {
          "shopLogin": document.getElementById("shopLogin").value,
          "paymentType": "CREDITCARD",
          "paymentTypeDetails": {
            "creditcard": {
              "token": creditcardToken
            }
          }
        };
      }
      else
      {
        axerveBodyRequest = {
          "shopLogin": document.getElementById("shopLogin").value,
          "paymentType": "CREDITCARD",
          "paymentTypeDetails": {
            "creditcard": {
              "number": document.getElementById("cardNumber").value.replace(/\s/g,''),
              "expMonth": document.getElementById("expiryMonth").value,
              "expYear": document.getElementById("expiryYear").value.substring(2),
              "CVV": document.getElementById("issueNumber").value,
              "DCC": null
            }
          }
        }
        if(document.getElementById("saveCard")
            && document.getElementById("saveCard").checked)
        {
          axerveBodyRequest.paymentTypeDetails.creditcard.requestToken = "MASKEDPAN";
        }
      }
      fetch(document.getElementById('paymentSubmitUrl').value,
        {
          method: 'POST',
          headers: {
            'paymentToken': document.getElementById("paymentToken").value,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(axerveBodyRequest)
        })
      .then(function(response){ return response.json(); })
      .then(function(data){
          if('0' === data.error.code)
          {
            if('8006' === data.payload.transactionErrorCode)
            {
              const threeDSRedirect = data.payload.userRedirect.href;
              window.location.href = threeDSRedirect;
            }
            else
            {
              const redirect = data.payload.userRedirect.href;
              const responseRedirect = redirect
                  .concat('&').concat('paymentToken=')
                  .concat(document.getElementById('paymentToken').value)
                  .concat('&').concat('paymentID=')
                  .concat(data.payload.paymentID);
              window.location.href = responseRedirect;
            }
          }
          else
          {
            if(!alert(data.error.description)){window.location.reload();}
          }
      })

    });
    //redirect enter key on submitAxerve
    if($(ctx.SELECTORS.paymentcreditCardForm).length) {
      $(document).on('keydown',$(ctx.SELECTORS.paymentcreditCardForm), function(e) {
        const target = e.target;
        const key = e.key;
        if (key === "Enter") {
          e.preventDefault();
          if(target.tagName && target.tagName.toLowerCase() !== "input" && target.tagName.toLowerCase() !== "select" && !target.classList.contains("js-tooltip")){
            $(ctx.SELECTORS.anchorSubmitSelectorAxerve).trigger('click');
          }
        }
      });
    }
  }


  // MAIN EVENTS
  bindEvents() {
    let ctx = this;

    if(this.checkoutForm) {

      this.checkoutForm.addEventListener("keydown", (e) => {
        const target = e.target;
        const key = e.key;

        if(target.tagName && (target.tagName.toLowerCase() == "input" || target.tagName.toLowerCase() == "select") && key && key.toLowerCase() == "enter") {
          e.preventDefault();
        }
      });

      this.checkoutForm.addEventListener("submit", (event) => {
        console.log('🚀 CHECKOUT FORM SUBMIT EVENT TRIGGERED');

        let form = event.target;
        let cta = form.querySelector(ctx.SELECTORS.anchorSubmitSelector);

        // Log di tutti i campi billing address per debug
        const billingFields = form.querySelectorAll('[name*="billingAddress"]');
        console.log('📋 Billing Address Fields Status:', {
          totalFields: billingFields.length,
          fields: Array.from(billingFields).map(field => ({
            name: field.name,
            required: field.required,
            disabled: field.disabled,
            value: field.value,
            type: field.type,
            visible: !field.hidden && field.offsetParent !== null
          }))
        });

        // Controlla se ci sono campi required ma disabled
        const problematicFields = Array.from(billingFields).filter(field =>
          field.required && field.disabled
        );

        if (problematicFields.length > 0) {
          console.error('❌ PROBLEMATIC FIELDS FOUND (required but disabled):',
            problematicFields.map(f => ({ name: f.name, required: f.required, disabled: f.disabled }))
          );
        }

        // Log della serializzazione del form
        console.log('📤 Form Data:', $(form).serialize());

        if(cta) {
          // adding loading class
          cta.classList.add(ctx.CLASSES.addingClass);
          // after click i disable it
          cta.setAttribute('disabled', 'true');
        }
      });
    }

    // fast checkout need invoice button
    $(this.SELECTORS.needInvoiceFastSelector).on('change', function () {
      let needInvoiceInput = document.querySelector('.js-fast-need-invoice');
      console.log(needInvoiceInput.checked);
      let contextPath = document.getElementsByTagName('HTML')[0].getAttribute("data-context");
      $.ajax({
        type: "POST",
        url: contextPath + "/checkout?addinvoice=true&fast=true",
        data: {
          'needInvoice': needInvoiceInput.checked
        },
        dataType: "json",
        success: function (resp) {
          console.log('Successfully set needInvoice in fast checkout');
          if (resp.reload){
            window.location.reload();
          }
        },
        error: function (xhr) {
          console.log('Error setting needInvoice in fast checkout');
          needInvoiceInput.checked = false;
          document.querySelector('.need-invoice-error')?.classList.add('show');
          setTimeout(() => {
            document.querySelector('.need-invoice-error')?.classList.remove('show');
          }, 3000);
        }
      });
    });

    // on show invoice form
    this.$on(this.$customEvents.CHECKOUT_EVENTS.showInvoiceForm, (e) => {
      this.enableNewBillingAddressSelector(true,e.payload.box);
    });
    // on hide invoice form
    this.$on(this.$customEvents.CHECKOUT_EVENTS.hideInvoiceForm, (e) => {
      this.enableNewBillingAddressSelector(false,e.payload.box);
    });

    // on privacy checkbox change
    this.$on(this.$customEvents.CHECKOUT_EVENTS.privacyChange, (e) => {
      // trigger submit enabling
      this.enableCheckoutSubmit(e.payload.checkboxes);
    });

    // on change payment method
    this.$on(this.$customEvents.CHECKOUT_EVENTS.paymentSelected, (e) => {
      this.shippingAddressBraintree();
      //
      let paymentMethod = e.payload.payment;
      this.updatePaymentMethod(paymentMethod);
    });

    this.$on(this.$customEvents.MODAL_EVENTS.afterOpen, () => {
      let modal = document.querySelector('.vex-content');
      if (modal != null) {
        this.restyleModalDynamically(modal);
      }
    });

    // DOM LISTENER
    // select shipping address - show and enable new address if necessary
    $(document).on('change', this.SELECTORS.shippingAddressSelector, {selectors: this.SELECTORS}, function (event) {
      let checked = this.checked;
      let fieldsBox = ctx.$component.querySelector(ctx.SELECTORS.shippingAddressBoxSelector);
      // fast checkout
      if (fieldsBox == null) {
        fieldsBox = document.querySelector(ctx.SELECTORS.shippingAddressBoxSelector);
      }
      // if i am in fast and i want to show
      if (this.classList.contains(ctx.CLASSES.fastClass)) {
        let target = document.querySelector(this.getAttribute('data-show'));
        if (target != null) {
          // show or hidden
          if (checked) {
            target.classList.remove(this.getAttribute('data-toggle-class'));
          }
        }
      } else {
        // if i am in fast and i select another address than new
        let fastAddress = document.querySelector(ctx.SELECTORS.fastAddress);
        if (fastAddress != null) {
          let newAddressFastBox = document.querySelector(fastAddress.getAttribute('data-show'));
          newAddressFastBox.classList.add(ctx.CLASSES.hiddenClass);
        }
      }
      let fields = fieldsBox.querySelectorAll('input, select');
      // shippingAddressNewSelector
      let newAddressSelected = this.classList.contains(ctx.SELECTORS.shippingAddressNewSelector.replace('.', ''));
      // if is checked i enable, if not, i disable all fields
      if (checked && newAddressSelected) {
        ctx.enableFields(fields,true);
        // set the provinces
        let shippingRegion = ctx.$component.querySelector(ctx.SELECTORS.shippingRegion);
        // fast
        if (shippingRegion == null) shippingRegion = document.querySelector(ctx.SELECTORS.shippingRegion);
        // ctx.setProvinceField( ctx.$component.querySelector(ctx.SELECTORS.shippingRegion).value );
        ctx.setProvinceField(shippingRegion.value );
      } else {
        ctx.enableFields(fields,false);
      }
    });

    // if i trigger the need invoice checkbox i should show the form but only with position enabled
    $(document).on('change', ctx.SELECTORS.needInvoiceSelector, {selectors: ctx.SELECTORS}, function (event) {
      let checked = this.checked;
      // if is checked
      if (checked) {
        ctx.log('enable invoice form');
        // launch event
        ctx.$emit(ctx.$customEvents.CHECKOUT_EVENTS.showInvoiceForm,{box:this.closest('[data-show]')});
      } else {
        ctx.log('disable invoice form');
        // launch event
        ctx.$emit(ctx.$customEvents.CHECKOUT_EVENTS.hideInvoiceForm,{box:this.closest('[data-show]')});
      }
    });
    // triggered label
    this.triggerLabel.render();
    // if in dom ready i need to trigger it
    $(document).ready(() =>{

      // If  privacyInputs are checked, i have to enable the submit button
      // this.enableCheckoutSubmit(this.privacyInputs);

      // if is checked , i have to set up the labels
      if (this.needInvoiceSelector != null && this.needInvoiceSelector.checked) {
        this.triggerLabel.setLabel();
      }

      if (this.billingAddressNewSelector && this.billingAddressNewSelector.checked) {
        var countryCode = $(this.SELECTORS.billingRegion).val();
        CheckoutComponent.setProvinceField(countryCode);
      }
      if(this.checkoutModifyAddress) {
        this.triggerLabel.setLabel();
      }

    });

    // enable disable new billing address form
    $(document).on('change', this.SELECTORS.billingAddressNewSelector, {selectors: this.SELECTORS}, function (event) {
      console.log('🏠 NEW BILLING ADDRESS SELECTOR CHANGED:', {
        checked: this.checked,
        selector: ctx.SELECTORS.billingAddressNewSelector,
        element: this
      });

      let checked = this.checked;
      let fieldsBox = ctx.$component.querySelector(ctx.SELECTORS.billingAddressNewBox);
      // fast checkout
      if (fieldsBox == null) {
        fieldsBox = document.querySelector(ctx.SELECTORS.billingAddressNewBox);
      }

      console.log('🏠 Fields box found:', fieldsBox);

      if (this.classList.contains('fast')) {
        let target = document.querySelector(this.getAttribute('data-show'));
        if (target != null) {
          // show or hidden
          if (checked) {
            target.classList.remove(this.getAttribute('data-toggle-class'));
          } else {
            target.classList.add(this.getAttribute('data-toggle-class'));
          }
        }
      }
      // take all fields
      let fields = fieldsBox.querySelectorAll('input, select');
      console.log('🏠 Found fields in billing address box:', fields.length);

      // if is checked i enable, if not, i disable all fields
      if (checked) {
        console.log('🏠 ENABLING billing address fields');
        ctx.enableFields(fields,true);
        // set the provinces
        let billingRegion = ctx.$component.querySelector(ctx.SELECTORS.billingRegion);
        // fast
        if (billingRegion == null) billingRegion = document.querySelector(ctx.SELECTORS.billingRegion);
        if (billingRegion == null) billingRegion = document.querySelector(ctx.SELECTORS.billingRegionSelect);
        //
        ctx.setProvinceField(billingRegion.value );
      } else {
        console.log('🏠 DISABLING billing address fields');
        ctx.enableFields(fields,false);
      }
    });

    // select billing addresses - new one if necessary
    $(document).on('change', this.SELECTORS.billingAddressSelector, {selectors: this.SELECTORS}, function (event) {
      console.log('🏠 BILLING ADDRESS SELECTOR CHANGED:', {
        checked: this.checked,
        value: this.value,
        selector: ctx.SELECTORS.billingAddressSelector,
        element: this
      });

      let checked = this.checked;
      //
      let fieldsBox = ctx.$component.querySelector(ctx.SELECTORS.billingAddressAddNew);
      // fast checkout
      if (fieldsBox == null) {
        fieldsBox = document.querySelector(ctx.SELECTORS.billingAddressAddNew);
      }

      console.log('🏠 Billing address fields box:', fieldsBox);

      // fast and i select another address than the new one with the form
      if (this.getAttribute('data-show') == null && this.classList.contains(ctx.CLASSES.fastClass)) {
        let fastAddress = document.querySelector(ctx.SELECTORS.billingAddressNewBox);
        if (fastAddress != null) {
          console.log('🏠 Hiding fast address form and disabling fields');
          fastAddress.classList.add(ctx.CLASSES.hiddenClass);
          // disable fields
          let fields = fastAddress.querySelectorAll('input, select');
          ctx.enableFields(fields,false);
        }
      }
      if (fieldsBox != null) {
        let fields =  fieldsBox.querySelectorAll('input:not(#new-billing-address input), select:not(#new-billing-address select)');
        console.log('🏠 Found fields in billing address selector:', fields.length);

        // shippingAddressNewSelector
        let newAddressSelected = this.classList.contains(ctx.SELECTORS.billingAddressAddNewSelector.replace('.', ''));
        console.log('🏠 New address selected:', newAddressSelected);

        // if is checked i enable, if not, i disable all fields
        if (checked && newAddressSelected) {
          console.log('🏠 ENABLING billing address selector fields');
          ctx.enableFields(fields,true);
          // set the provinces
          // ctx.setProvinceField( ctx.$component.querySelector(ctx.SELECTORS.billingRegionNew).value );
          // launch event
          ctx.$emit(ctx.$customEvents.CHECKOUT_EVENTS.showInvoiceFormLabels);
        } else {
          console.log('🏠 DISABLING billing address selector fields');
          ctx.enableFields(fields,false);
        }
      }
    });

    // CC selection
    $(document).on('click', this.SELECTORS.paymentCards, {selectors: this.SELECTORS}, function (event) {
      let selectors = event.data.selectors;
      let newCardPanel = document.querySelector(ctx.SELECTORS.newCardPanel);
      // check
      if (newCardPanel == null) return false;
      //
      $(selectors.paymentTokenFlag).prop('checked', false);
      $(this).find(selectors.paymentTokenFlag).prop('checked', true);
      // show newcard
      if ($(this).find(selectors.paymentTokenFlag).val()=='newcard') {
        newCardPanel.classList.remove('panel-hidden');
        newCardPanel.classList.add('panel-show');
      } else {
        newCardPanel.classList.add('panel-hidden');
        newCardPanel.classList.remove('panel-show');
      }
    });

    // CC save
    $(this.SELECTORS.newCardSave).on('change', function () {
      $('input[name="merchant_defined_data9"]').val($(this).is(':checked') ? 'true' : 'false');
    });

    if (this.paymentGiftcardFormById !== null) {

      this.paymentGiftcardFormById.addEventListener("keydown", (e) => {
        const target = e.target;
        const key = e.key;

        if (target.tagName && (target.tagName.toLowerCase() == "input" || target.tagName.toLowerCase() == "select") && key && key.toLowerCase() == "enter") {
          e.preventDefault();
        }
      });
    }

    if(this.paymentGiftcardForm !== null){
      this.paymentGiftcardForm.addEventListener('submit',(event) => {
        event.preventDefault();
        const submitBtn = this.paymentGiftcardForm.querySelector("button");
        if(submitBtn?.type === "submit"){
          submitBtn.disabled = true;
          submitBtn.classList.add("adding");
        }
        this.paymentGiftcardForm.submit();
      })
    }
  }

  render() {
    //
    this.log('Rendering...');
    //
    let ctx = this;
    // externals
    this.triggerLabel = new TriggeredLabelsComponent();
    this.CreditCardValidator = new CreditCardValidator();
    // main events
    this.bindEvents();
    // selection of payment
    this.paymentSelection();
    // enable buttons
    this.privacyCheckSelect();
    //enalbe form if billing address selected
    if(this.needInvoiceSelector != null && this.needInvoiceSelector.checked && this.invoiceCheckboxWrapper != null) {
      this.enableNewBillingAddressSelector(true, this.invoiceCheckboxWrapper);
    }
    // BRAINTREE
    this.braintreeInit();
    // submit form
    this.submitCheckoutForm();
    // submit axerve form
    this.submitAxerveCheckoutForm();

  }


  ///////////////////////////////////////////////////////////////
  // BRAINTREE
  ///////////////////////////////////////////////////////////////

  braintreeInit() {
    let ctx = this;
    // init braintree comms
    let braintreeButtonSelector = this.$component.querySelector(this.SELECTORS.braintreeButtonSelector);
    if (braintreeButtonSelector != null || document.querySelector('.giftcard.giftcard-addmore #paypal-button-container') != null) {
      // new request
      let xhttp = new XMLHttpRequest();
      xhttp.onreadystatechange = function(){
        if(this.readyState == 4 && this.status == 200){
          ctx.startBraintreeScript(this.response);
        }
      };
      xhttp.open('GET', window.location.pathname + '?braintree=clientToken', true);
      xhttp.send();
    }
  }


  // CLIENT + PAYPAL
  startBraintreeScript(token){
    const amountVal = parseFloat($('#amountValue').val());
    const currencyVal = $('#currencyValue').val();
    const braintreeVault = ($('#braintreeVault').val().toLowerCase() === 'true');
	  // Create a client.
	   if (braintreeVault) {
      braintree.client.create({
        authorization: token
      }).then((clientInstance) => {
      // Create a PayPal Checkout component.
        return braintree.paypalCheckout.create({
          autoSetDataUserIdToken: true,
          client: clientInstance
        });
      }).then((paypalCheckoutInstance) => {
        return paypalCheckoutInstance.loadPayPalSDK({
          //vault: true
          components: 'buttons,messages',
          currency: currencyVal,
          intent: 'authorize',
          'enable-funding': 'paylater'
        });
      }).then((paypalCheckoutInstance) => {
        var buttonVault = paypal.Buttons({
          fundingSource: paypal.FUNDING.PAYPAL,

          style: {
            color: 'black',
            shape: 'rect',
            label: 'checkout',
            tagline: false,
            height: 50
          },

          createOrder: () => {
            const parameters = 'validate-braintree-paypal-address=true&checkoutForm=' + $('#checkoutForm').serialize();
            let xhttp = new XMLHttpRequest();
            console.log('XHTTP params ',parameters);
            return paypalCheckoutInstance.createPayment({
              flow: 'checkout', // Required
              amount: amountVal, // Required
              currency: currencyVal, // Required, must match the currency passed in with loadPayPalSDK
              requestBillingAgreement: true,
              intent: 'authorize', // Must match the intent passed in with loadPayPalSDK

              enableShippingAddress: true,
              shippingAddressEditable: false,
              shippingAddressOverride: this.shippingAddressBraintree()
            }).then((data) => {
              console.log("orderID: " , data);
              xhttp.open('GET', window.location.pathname + '?' + parameters, false);
              xhttp.send();
              console.log('xhttp ',xhttp);
              if (xhttp.status === 200){
                if (xhttp.response != 'OK'){
                  if (window.location.search == ""){
                    $('#checkoutForm').submit();
                  }
                  return data;
                } else {
                  console.log('xhttp response ',xhttp.response);
                }
              } else {
                console.log('Status is: ' , xhttp.status);
                return data;
              }
              return data;
            }).catch((err) => {
              console.log('ERROR ',err.message);
              if (window.location.search == ""){
                $('#checkoutForm').submit();
              }
            });
          },

          onApprove: (data, actions) => {
            let ctx = this;
            ctx.showOverlay();
            return paypalCheckoutInstance.tokenizePayment(data).then((payload) => {
              // Submit `payload.nonce` to your server
              console.log('payload ',payload);
              const parameters = 'do-checkout=true&paymentNonce=' + payload.nonce + '&checkoutForm=' + $('#checkoutForm').serialize();
              let xhr = new XMLHttpRequest();
              xhr.onreadystatechange = function(){
                if (this.readyState == 4) {
                  if (this.status == 200) {
                  if (window.location.href != this.responseURL) {
                    // this.responseURL contains the string URL to redirect to
                    window.location.href = this.responseURL;
                    } else {
                      if(window.location.search == ""){
                        $('#checkoutForm').submit();
                      }
                    }
                  } else {
                    console.log('Error: ' + this.status);
                  }
                  ctx.hideOverlay();
                }
              };
              xhr.open('POST', window.location.pathname, true);
              //Send the proper header information along with the request
              xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');
              xhr.send(parameters);

            }).catch((err) => {
                console.error(err.message);
                ctx.hideOverlay();
            });
          },

          onCancel: (data) => {
            console.log('PayPal payment cancelled', JSON.stringify(data, 0, 2));
            console.log(data);
          },

          onError: (err) => {
            console.error('PayPal error', err);
          }
        });
         buttonVault.render('#paypal-checkout');

                   var buttonPayLater = paypal.Buttons({
                       fundingSource: paypal.FUNDING.PAYLATER,

                        createOrder: () => {
                            const parameters = 'validate-braintree-paypal-address=true&checkoutForm=' + $('#checkoutForm').serialize();
                            let xhttp = new XMLHttpRequest();
                            console.log('XHTTP params ',parameters);
                            return paypalCheckoutInstance.createPayment({
                              flow: 'checkout', // Required
                              amount: amountVal, // Required
                              currency: currencyVal // Required, must match the currency passed in with loadPayPalSDK
                            }).then((data) => {
                              console.log("orderID: " , data);
                              xhttp.open('GET', window.location.pathname + '?' + parameters, false);
                              xhttp.send();
                              console.log('xhttp ',xhttp);
                              if (xhttp.status === 200){
                                if (xhttp.response != 'OK'){
                                  if (window.location.search == ""){
                                    $('#checkoutForm').submit();
                                  }
                                  return data;
                                } else {
                                  console.log('xhttp response ',xhttp.response);
                                }
                              } else {
                                console.log('Status is: ' , xhttp.status);
                                return data;
                              }
                              return data;
                            }).catch((err) => {
                              console.log('ERROR ',err.message);
                              if (window.location.search == ""){
                                $('#checkoutForm').submit();
                              }
                            });
                          },


                       onApprove: (data, actions) => {
                         let ctx = this;
                         ctx.showOverlay();
                         return paypalCheckoutInstance.tokenizePayment(data).then((payload) => {
                           // Submit `payload.nonce` to your server
                           console.log(payload);
                           const parameters = 'do-checkout=true&paymentNonce=' + payload.nonce + '&checkoutForm=' + $('#checkoutForm').serialize();
                           let xhr = new XMLHttpRequest();
                           xhr.onreadystatechange = function () {
                             if (this.readyState == 4) {
                               if (this.status == 200) {
                                 if (window.location.href != this.responseURL) {
                                   // this.responseURL contains the string URL to redirect to
                                   window.location.href = this.responseURL;
                                 } else {
                                   if (window.location.search == "") {
                                     $('#checkoutForm').submit();
                                   }
                                 }
                               } else {
                                 console.log('Error: ' + this.status);
                               }
                               ctx.hideOverlay();
                             }
                           };
                           xhr.open('POST', window.location.pathname, true);
                           //Send the proper header information along with the request
                           xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');
                           xhr.send(parameters);

                         }).catch((err) => {
                           console.error(err.message);
                           ctx.hideOverlay();
                         });
                       },

                       onCancel: (data) => {
                         console.log('onCancel');
                         console.log(data);
                         console.log('PayPal payment cancelled', JSON.stringify(data, 0, 2));
                       },

                       onError: (err) => {
                         console.log('something wrong !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!');
                         console.error('PayPal error', err);
                       }
                   });
                   if (buttonPayLater.isEligible()){
                     buttonPayLater.render('#paypal-paylater');
                   }

      }).then(() => {
        // The PayPal button will be rendered in an html element with the ID
        // `paypal-button`. This function will be called when the PayPal button
        // is set up and ready to be used
      }).catch((clientErr) => {
        console.error(clientErr);
      });
     } else {
                console.log("vault inactive");
                braintree.client.create({
                  authorization: token
                }).then((clientInstance) => {
                  // Create a PayPal Checkout component.
                  return braintree.paypalCheckout.create({
                    client: clientInstance
                  });
                }).then((paypalCheckoutInstance) => {
                  return paypalCheckoutInstance.loadPayPalSDK({
                      vault: false,
                      currency: currencyVal,
                      //intent: 'capture'
                  });
                }).then((paypalCheckoutInstance) => {
                  return paypal.Buttons({
                    fundingSource: paypal.FUNDING.PAYPAL,
                    style: {
                      color: 'black',
                      shape: 'rect',
                      label: 'checkout',
                      tagline: false,
                      height: 50
                    },

                     createOrder: () => {
                       const parameters = 'validate-braintree-paypal-address=true&checkoutForm=' + $('#checkoutForm').serialize();
                       let xhttp = new XMLHttpRequest();
                       console.log('XHTTP params ',parameters);
                       return paypalCheckoutInstance.createPayment({
                         flow: 'checkout', // Required
                         amount: amountVal, // Required
                         currency: currencyVal // Required, must match the currency passed in with loadPayPalSDK
                       }).then((data) => {
                         console.log("orderID: " , data);
                         xhttp.open('GET', window.location.pathname + '?' + parameters, false);
                         xhttp.send();
                         console.log('xhttp ',xhttp);
                         if (xhttp.status === 200){
                           if (xhttp.response != 'OK'){
                             if (window.location.search == ""){
                               $('#checkoutForm').submit();
                             }
                             return data;
                           } else {
                             console.log('xhttp response ',xhttp.response);
                           }
                         } else {
                           console.log('Status is: ' , xhttp.status);
                           return data;
                         }
                         return data;
                       }).catch((err) => {
                         console.log('ERROR ',err.message);
                         if (window.location.search == ""){
                           $('#checkoutForm').submit();
                         }
                       });
                     },


                onApprove: (data, actions) => {
                  let ctx = this;
                  ctx.showOverlay();
                  return paypalCheckoutInstance.tokenizePayment(data).then((payload) => {
                    // Submit `payload.nonce` to your server
                    console.log('payload ',payload);
                    const parameters = 'do-checkout=true&paymentNonce=' + payload.nonce + '&checkoutForm=' + $('#checkoutForm').serialize();
                    let xhr = new XMLHttpRequest();
                    xhr.onreadystatechange = function(){
                      if (this.readyState == 4) {
                        if (this.status == 200) {
                        if (window.location.href != this.responseURL) {
                          // this.responseURL contains the string URL to redirect to
                          window.location.href = this.responseURL;
                          } else {
                            if(window.location.search == ""){
                              $('#checkoutForm').submit();
                            }
                          }
                        } else {
                          console.log('Error: ' + this.status);
                        }
                        ctx.hideOverlay();
                      }
                    };
                    xhr.open('POST', window.location.pathname, true);
                    //Send the proper header information along with the request
                    xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');
                    xhr.send(parameters);

                  }).catch((err) => {
                      console.error(err.message);
                      ctx.hideOverlay();
                  });
                },

                    onCancel: (data) => {
                      console.log('onCancel');
                      console.log(data);
                      console.log('PayPal payment cancelled', JSON.stringify(data, 0, 2));
                    },

                    onError: (err) => {
                      console.log('something wrong !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!');
                      console.error('PayPal error', err);
                    }
                  }).render('#paypal-checkout');
                }).then(() => {
                  // The PayPal button will be rendered in an html element with the ID
                  // `paypal-button`. This function will be called when the PayPal button
                  // is set up and ready to be used
                }).catch((clientErr) => {
                  console.error(clientErr);
                });
              }
     }

  // overlay functions
  hideOverlay(){
    document.querySelector(this.SELECTORS.braintreeOverlay).classList.add('loaded');
  }
  showOverlay(){
    document.querySelector(this.SELECTORS.braintreeOverlay).classList.remove('loaded');
    document.querySelector(this.SELECTORS.braintreeOverlay).classList.add('loader');
  }

  shippingAddressBraintree(){
    const $oneClickShippingAddress = $(this.SELECTORS.oneClickShippingAddress);
    // init address obj
    let addressBox = null;
    let shippingAddressFields = null;
    let shippingAddressObject = {};
    // get selectors
    let selectedShippingAddress = this.$component.querySelector(this.SELECTORS.shippingAddressSelector+":checked");

    if ( selectedShippingAddress == null ||
        selectedShippingAddress.classList.contains(this.SELECTORS.shippingAddressNewSelector.replace('.', '')) ||
        document.querySelector(this.SELECTORS.addressClickAndCollect) != null
      ) {
      // prefix
      let prefix = 'shippingAddress';
      // new address or GUEST Checkout
      addressBox = this.$component.querySelector(this.SELECTORS.shippingAddressBoxSelector);
      // GC part
      if (addressBox == null) {
        addressBox = document.querySelector(this.SELECTORS.giftcardAddmorePaypalForm);
      }
      //C&C part
      if (document.querySelector(this.SELECTORS.addressClickAndCollect) != null) {
        prefix = 'paymentAddress';
      }
      // fields
      // province selector exception
      let provinceSelector = 'select[name="'+prefix+'.province"]';
      let provinceField = addressBox.querySelector(provinceSelector);
      if (addressBox.querySelector(provinceSelector) == null) {
        provinceSelector = 'input[name="'+prefix+'.province"]';
      }
      shippingAddressFields = {
        'recipientName' : 'input[name="shippingAddress.firstName"] , input[name="shippingAddress.lastName"]',
        'line1' : 'input[name="'+prefix+'.line1"]',
        'line2' : 'input[name="'+prefix+'.line2"]',
        'phone' : 'input[name="'+prefix+'.phone"]',
        'city' : 'input[name="'+prefix+'.town"]',
        'countryCode' : 'input[name="'+prefix+'.country.isocode"]',
        'postalCode' : 'input[name="'+prefix+'.postalCode"]',
        'state': provinceSelector,
      };
    } else {
      // list address
      addressBox = this.$component.querySelector('#shipping-address-'+selectedShippingAddress.value);
      shippingAddressFields = {
        'recipientName' : '.name',
        'line1' : '.addressLine1',
        'line2' : '.addressLine2',
        'phone' : '.phone',
        'city' : '.town',
        'countryCode' : '.country-isocode',
        'postalCode' : '.postalcode',
        'state' : '.province',
      };
    }

    // loop for selectors
    let i = 0;
    let keys = Object.keys(shippingAddressFields);
    let len = keys.length;
    for (i = 0; i < len; i++) {
      // init value
      let addressValue = '';
      // take the fields
      let fieldBoxes = addressBox.querySelectorAll(shippingAddressFields[keys[i]]);
      fieldBoxes.forEach( fieldBox => {
        // check if is null
        if (fieldBox != null) {
          // put into temp var
          if (fieldBox.nodeName == 'SELECT' || fieldBox.nodeName == 'INPUT') {
            addressValue = addressValue + fieldBox.value;
          } else {
            // put into temp var
            if (fieldBox.innerHTML != '') {
              addressValue = addressValue + fieldBox.innerHTML;
            }
          }
        }
      });
      // if i have a value , put inside the address object
      if (addressValue != '' ) shippingAddressObject[keys[i]] = addressValue;
    }
    this.log('braintree shippingAddressObject',shippingAddressObject);
    return shippingAddressObject;
  }
}
